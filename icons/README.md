# PWA Icons

This directory should contain the app icons for the PDF Print Tools PWA.

## Required Icon Sizes

The following icon files are referenced in the manifest.json and should be created:

- `icon-72x72.png` (72x72 pixels)
- `icon-96x96.png` (96x96 pixels)
- `icon-128x128.png` (128x128 pixels)
- `icon-144x144.png` (144x144 pixels)
- `icon-152x152.png` (152x152 pixels)
- `icon-192x192.png` (192x192 pixels)
- `icon-384x384.png` (384x384 pixels)
- `icon-512x512.png` (512x512 pixels)

## Creating Icons

You can create these icons using:

1. **Online tools:**
   - [PWA Builder Icon Generator](https://www.pwabuilder.com/imageGenerator)
   - [Favicon.io](https://favicon.io/)
   - [RealFaviconGenerator](https://realfavicongenerator.net/)

2. **Design software:**
   - Adobe Illustrator/Photoshop
   - GIMP (free)
   - Canva
   - Figma

3. **Simple approach:**
   - Create a 512x512 PNG with a PDF-related icon (📄 or similar)
   - Use an online tool to generate all the required sizes

## Icon Design Guidelines

- Use a simple, recognizable design
- Ensure good contrast and visibility at small sizes
- Consider using the 📄 emoji or a document/PDF-related icon
- Use the app's theme colors (#2c3e50 background, white foreground)
- Make sure the icon works well on both light and dark backgrounds

## Temporary Solution

Until proper icons are created, the PWA will still work but may show a default browser icon in the app launcher.
