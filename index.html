<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Print Tools</title>
    
    <!-- PWA Meta Tags -->
    <meta name="description" content="A Progressive Web App for viewing and printing PDF documents using PDF.js">
    <meta name="theme-color" content="#2c3e50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="PDF Print">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- Redirect to main app -->
    <meta http-equiv="refresh" content="0; url=test-pdf-print.html">
    
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        .redirect-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            text-align: center;
            padding: 20px;
        }
        
        .redirect-message {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            max-width: 500px;
        }
        
        .redirect-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        .redirect-links {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .redirect-link {
            background: #3498db;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 6px;
            transition: background-color 0.3s;
        }
        
        .redirect-link:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="redirect-container">
        <div class="redirect-message">
            <div class="redirect-icon">📄</div>
            <h1>PDF Print Tools</h1>
            <p>Redirecting to the main application...</p>
            <p>If you're not redirected automatically, click one of the links below:</p>
            
            <div class="redirect-links">
                <a href="test-pdf-print.html" class="redirect-link">🔄 Multi-Method Test</a>
                <a href="test-pdfjs-print.html" class="redirect-link">📄 PDF.js Test</a>
                <a href="install-check.html" class="redirect-link">🔍 Install Check</a>
            </div>
        </div>
    </div>
    
    <script>
        // JavaScript redirect as backup
        setTimeout(() => {
            if (window.location.pathname.endsWith('index.html') || window.location.pathname.endsWith('/')) {
                window.location.href = 'test-pdf-print.html';
            }
        }, 1000);
    </script>
</body>
</html>
