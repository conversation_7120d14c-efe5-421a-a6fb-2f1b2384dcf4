<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF.js Print Test</title>

    <!-- PWA Meta Tags -->
    <meta name="description" content="A Progressive Web App for viewing and printing PDF documents using PDF.js">
    <meta name="theme-color" content="#2c3e50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="PDF Print">
    <meta name="msapplication-TileColor" content="#2c3e50">
    <meta name="msapplication-config" content="browserconfig.xml">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="152x152" href="icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="192x192" href="icons/icon-192x192.png">

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="icons/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icons/icon-16x16.png">

    <link rel="stylesheet" href="shared-styles.css">
    <style>
        /* PDF.js specific styles */
        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .page-info {
            font-weight: bold;
            color: #2c3e50;
        }

        .status {
            color: #7f8c8d;
            font-style: italic;
        }

        .canvas-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            overflow: auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }

        #pdf-canvas {
            border: 1px solid #bdc3c7;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            max-width: 100%;
            height: auto;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: #7f8c8d;
            font-size: 18px;
        }

        /* Print styles */
        @media print {
            body * {
                visibility: hidden;
            }

            #pdf-canvas {
                visibility: visible !important;
                position: absolute;
                top: 0;
                left: 0;
                width: 100% !important;
                height: auto !important;
                max-width: none !important;
                border: none !important;
                box-shadow: none !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            @page {
                margin: 0.5in;
                size: auto;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <h1>📄 PDF.js Print Test</h1>
            <a href="test-pdf-print.html" class="nav-link">🔄 Multi-Method Test</a>
        </div>
        <div class="button-group">
            <button class="btn" onclick="loadPDF()">📂 Load PDF</button>
            <button class="btn btn-print" onclick="printPDF()" id="print-btn" disabled>🖨️ Print PDF</button>
            <button class="btn" onclick="installPWA()" id="install-btn" style="display: none;">📱 Install App</button>
        </div>
    </div>
    
    <div class="content">
        <div class="controls">
            <div class="page-controls">
                <button class="btn" onclick="previousPage()" id="prev-btn" disabled>⬅️ Previous</button>
                <span class="page-info" id="page-info">Page: - / -</span>
                <button class="btn" onclick="nextPage()" id="next-btn" disabled>➡️ Next</button>
            </div>
            <div class="status" id="status">Click "Load PDF" to start</div>
        </div>
        
        <div class="canvas-container">
            <div class="loading" id="loading">
                Ready to load PDF document
            </div>
            <canvas id="pdf-canvas" style="display: none;"></canvas>
        </div>
    </div>

    <!-- PDF.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.6.82/pdf.min.mjs" type="module"></script>
    
    <script type="module">
        // PDF.js configuration
        import * as pdfjsLib from 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.6.82/pdf.min.mjs';
        
        // Set worker source
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.6.82/pdf.worker.min.mjs';
        
        // Global variables
        let pdfDoc = null;
        let currentPage = 1;
        let totalPages = 0;
        let scale = 1.5;
        
        // PDF file path - change this to use a different PDF
        const PDF_FILE_PATH = 'data/veolia-protocol.pdf';
        
        // DOM elements
        const canvas = document.getElementById('pdf-canvas');
        const ctx = canvas.getContext('2d');
        const loading = document.getElementById('loading');
        const status = document.getElementById('status');
        const pageInfo = document.getElementById('page-info');
        const printBtn = document.getElementById('print-btn');
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        
        // Make functions global for onclick handlers
        window.loadPDF = loadPDF;
        window.printPDF = printPDF;
        window.previousPage = previousPage;
        window.nextPage = nextPage;
        
        async function loadPDF() {
            try {
                status.textContent = 'Loading PDF.js library...';
                loading.style.display = 'flex';
                loading.textContent = 'Loading PDF document...';
                canvas.style.display = 'none';
                
                console.log('Loading PDF:', PDF_FILE_PATH);
                
                // Load the PDF document
                const loadingTask = pdfjsLib.getDocument(PDF_FILE_PATH);
                pdfDoc = await loadingTask.promise;
                totalPages = pdfDoc.numPages;
                currentPage = 1;
                
                console.log(`PDF loaded successfully. Pages: ${totalPages}`);
                status.textContent = `PDF loaded: ${totalPages} page(s)`;
                
                // Render first page
                await renderPage(currentPage);
                
                // Enable controls
                printBtn.disabled = false;
                updatePageControls();
                
                loading.style.display = 'none';
                canvas.style.display = 'block';
                
            } catch (error) {
                console.error('PDF loading error:', error);
                status.textContent = `Error: ${error.message}`;
                loading.textContent = `Error loading PDF: ${error.message}`;
                alert(`PDF loading failed: ${error.message}`);
            }
        }
        
        async function renderPage(pageNum) {
            try {
                status.textContent = `Rendering page ${pageNum}...`;
                
                const page = await pdfDoc.getPage(pageNum);
                const viewport = page.getViewport({ scale: scale });
                
                canvas.width = viewport.width;
                canvas.height = viewport.height;
                
                const renderContext = {
                    canvasContext: ctx,
                    viewport: viewport
                };
                
                await page.render(renderContext).promise;
                
                status.textContent = `Page ${pageNum} of ${totalPages} rendered`;
                updatePageInfo();
                
                console.log(`Page ${pageNum} rendered successfully`);
                
            } catch (error) {
                console.error('Page rendering error:', error);
                status.textContent = `Error rendering page: ${error.message}`;
            }
        }
        
        function updatePageControls() {
            prevBtn.disabled = currentPage <= 1;
            nextBtn.disabled = currentPage >= totalPages;
        }
        
        function updatePageInfo() {
            pageInfo.textContent = `Page: ${currentPage} / ${totalPages}`;
            updatePageControls();
        }
        
        async function previousPage() {
            if (currentPage > 1) {
                currentPage--;
                await renderPage(currentPage);
            }
        }
        
        async function nextPage() {
            if (currentPage < totalPages) {
                currentPage++;
                await renderPage(currentPage);
            }
        }
        
        function printPDF() {
            if (!pdfDoc) {
                alert('Please load a PDF first');
                return;
            }
            
            console.log('Starting print process...');
            status.textContent = 'Preparing for print...';
            
            const userConfirm = confirm(
                `Print the current page (${currentPage} of ${totalPages})?\n\n` +
                `This will open your browser's print dialog.`
            );
            
            if (userConfirm) {
                try {
                    status.textContent = 'Opening print dialog...';
                    
                    // Focus window and trigger print
                    window.focus();
                    setTimeout(() => {
                        window.print();
                        status.textContent = 'Print dialog opened';
                        console.log('Print dialog opened');
                    }, 100);
                    
                } catch (error) {
                    console.error('Print failed:', error);
                    status.textContent = 'Print failed: ' + error.message;
                    alert('Print failed: ' + error.message);
                }
            } else {
                status.textContent = 'Print cancelled';
            }
        }
        
        // Auto-load PDF on page load
        window.addEventListener('load', () => {
            console.log('Page loaded, auto-loading PDF...');
            loadPDF();
        });

    </script>

    <!-- PWA Service Worker Registration -->
    <script>
        // Register service worker for PWA functionality
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('./sw.js')
                    .then((registration) => {
                        console.log('SW registered: ', registration);

                        // Check for updates
                        registration.addEventListener('updatefound', () => {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', () => {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // New content is available, prompt user to refresh
                                    if (confirm('New version available! Refresh to update?')) {
                                        window.location.reload();
                                    }
                                }
                            });
                        });
                    })
                    .catch((registrationError) => {
                        console.warn('SW registration failed: ', registrationError);

                        // Handle SSL certificate errors gracefully
                        if (registrationError.name === 'SecurityError') {
                            console.warn('SSL certificate issue detected. PWA features may be limited.');
                            console.warn('For development: Enable chrome://flags/#allow-insecure-localhost');
                        }

                        // PWA can still work without service worker for basic functionality
                        console.log('App will continue without service worker (no offline support)');
                    });
            });
        }

        // PWA Install Prompt
        let deferredPrompt;
        const installBtn = document.getElementById('install-btn');

        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA install prompt available');
            e.preventDefault();
            deferredPrompt = e;

            // Show the custom install button
            if (installBtn) {
                installBtn.style.display = 'inline-block';
            }
            console.log('App can be installed - install button shown');
        });

        // Install PWA function
        window.installPWA = async function() {
            if (deferredPrompt) {
                // Show the install prompt
                deferredPrompt.prompt();

                // Wait for the user to respond to the prompt
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`User response to the install prompt: ${outcome}`);

                if (outcome === 'accepted') {
                    console.log('User accepted the install prompt');
                } else {
                    console.log('User dismissed the install prompt');
                }

                // Clear the deferredPrompt
                deferredPrompt = null;

                // Hide the install button
                if (installBtn) {
                    installBtn.style.display = 'none';
                }
            } else {
                alert('Install prompt not available. The app may already be installed or your browser doesn\'t support PWA installation.');
            }
        };

        // Handle successful installation
        window.addEventListener('appinstalled', (evt) => {
            console.log('PWA was installed');
            // Hide the install button
            if (installBtn) {
                installBtn.style.display = 'none';
            }

            // Show a success message
            alert('App installed successfully! You can now find it in your app drawer or home screen.');
        });
    </script>
</body>
</html>
