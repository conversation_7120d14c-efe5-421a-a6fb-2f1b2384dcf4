# PDF Print Tools PWA

A Progressive Web App for viewing and printing PDF documents using PDF.js and other methods.

## Features

- 📱 **Progressive Web App** - Install on mobile and desktop devices
- 📄 **PDF.js Integration** - View and print PDFs using PDF.js library
- 🔄 **Multiple Print Methods** - Test different PDF printing approaches
- 📴 **Offline Support** - Works offline with cached content
- 🎨 **Responsive Design** - Works on all screen sizes

## PWA Features

### Installation
- The app can be installed on supported devices
- Look for the "Install" prompt in your browser
- On mobile: Add to Home Screen
- On desktop: Install via browser menu

### Offline Support
- Core functionality works offline
- PDF files are cached for offline viewing
- Offline page shown when network is unavailable

### Caching Strategy
- Static assets are cached on first visit
- PDF files are cached when accessed
- Service worker handles cache management

## Files Structure

```
├── manifest.json          # PWA manifest file
├── sw.js                  # Service worker for caching and offline support
├── browserconfig.xml      # Windows tile configuration
├── offline.html          # Offline fallback page
├── icons/                # App icons directory
│   └── README.md         # Instructions for creating icons
├── test-pdfjs-print.html # PDF.js print test page
├── test-pdf-print.html   # Multi-method print test page
├── shared-styles.css     # Shared CSS styles
└── data/                 # PDF files for testing
    ├── veolia-protocol.pdf
    └── pu.pdf
```

## Setup Instructions

1. **Serve the files** via a web server (required for PWA features)
2. **Create app icons** - See `icons/README.md` for instructions
3. **Test PWA features** - Use browser dev tools to test offline mode
4. **Install the app** - Use browser's install prompt

## Browser Support

- **Chrome/Edge**: Full PWA support
- **Firefox**: Most features supported
- **Safari**: Basic PWA support (iOS 11.3+)

## Development

### Testing PWA Features
1. Open browser dev tools
2. Go to Application/Storage tab
3. Test service worker registration
4. Test offline mode
5. Test install prompt

### Updating the App
- Increment version in `sw.js` cache name
- Service worker will update automatically
- Users will be prompted to refresh for updates

## Customization

### App Identity
- Edit `manifest.json` to change app name, colors, etc.
- Update meta tags in HTML files
- Replace icons in `icons/` directory

### Caching Strategy
- Modify `sw.js` to change what gets cached
- Add/remove files from `urlsToCache` array
- Customize offline behavior

## Notes

- PWA features require HTTPS in production
- Service worker registration happens automatically
- Install prompt is handled automatically
- Offline page provides fallback when network is unavailable
