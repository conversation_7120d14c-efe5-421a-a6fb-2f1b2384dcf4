<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Print Tools - Simple PWA</title>
    
    <!-- PWA Meta Tags -->
    <meta name="description" content="A Progressive Web App for viewing and printing PDF documents using PDF.js">
    <meta name="theme-color" content="#2c3e50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="PDF Print">
    
    <!-- PWA Manifest (Development version without service worker) -->
    <link rel="manifest" href="manifest-dev.json">
    
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        .simple-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .feature-card {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .install-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
            border-radius: 12px;
            margin: 20px 0;
        }
        
        .install-button {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid white;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s;
        }
        
        .install-button:hover {
            background: white;
            color: #667eea;
        }
        
        .ssl-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .ssl-warning h3 {
            margin-top: 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <h1>📱 Simple PWA Test</h1>
            <a href="test-pdfjs-print.html" class="nav-link">📄 PDF.js Test</a>
            <a href="test-pdf-print.html" class="nav-link">🔄 Multi-Method Test</a>
            <a href="install-check.html" class="nav-link">🔍 Install Check</a>
        </div>
    </div>
    
    <div class="simple-container">
        <div class="ssl-warning">
            <h3>🔒 SSL Certificate Notice</h3>
            <p>This version works with self-signed certificates by skipping the service worker registration.</p>
            <p><strong>To enable full PWA features:</strong></p>
            <ol>
                <li>Go to <code>chrome://flags/#allow-insecure-localhost</code></li>
                <li>Enable "Allow invalid certificates for resources loaded from localhost"</li>
                <li>Restart Chrome</li>
                <li>Then use the full PWA version</li>
            </ol>
        </div>
        
        <div class="install-section">
            <h2>📱 Install This App</h2>
            <p>Add this app to your home screen for a native app experience!</p>
            
            <button class="install-button" id="install-btn" onclick="installApp()" style="display: none;">
                📱 Install Now
            </button>
            
            <div id="install-status">
                <p>⏳ Checking install availability...</p>
            </div>
        </div>
        
        <div class="feature-card">
            <h3>✨ PWA Features Available</h3>
            <ul>
                <li>📱 <strong>Installable:</strong> Add to home screen</li>
                <li>🎨 <strong>App-like:</strong> Standalone display mode</li>
                <li>📄 <strong>PDF Tools:</strong> View and print PDFs</li>
                <li>🔄 <strong>Multiple Methods:</strong> Different printing approaches</li>
            </ul>
        </div>
        
        <div class="feature-card">
            <h3>🚀 Quick Links</h3>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <a href="test-pdfjs-print.html" class="btn">📄 PDF.js Test</a>
                <a href="test-pdf-print.html" class="btn">🔄 Multi-Method Test</a>
                <a href="install-check.html" class="btn">🔍 Install Check</a>
            </div>
        </div>
        
        <div class="feature-card">
            <h3>📋 Manual Installation</h3>
            <p><strong>Desktop Chrome/Edge:</strong></p>
            <ul>
                <li>Look for install icon in address bar</li>
                <li>Or go to Menu → Install "PDF Print Tools"</li>
            </ul>
            
            <p><strong>Mobile Chrome:</strong></p>
            <ul>
                <li>Menu (⋮) → "Add to Home screen"</li>
            </ul>
            
            <p><strong>Safari (iOS):</strong></p>
            <ul>
                <li>Share button → "Add to Home Screen"</li>
            </ul>
        </div>
    </div>
    
    <script>
        let deferredPrompt = null;
        const installBtn = document.getElementById('install-btn');
        const installStatus = document.getElementById('install-status');
        
        // Install app function
        async function installApp() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                
                if (outcome === 'accepted') {
                    installStatus.innerHTML = '<p>🎉 App installed successfully!</p>';
                } else {
                    installStatus.innerHTML = '<p>❌ Installation cancelled</p>';
                }
                
                deferredPrompt = null;
                installBtn.style.display = 'none';
            } else {
                alert('Install prompt not available. Try the manual installation methods below.');
            }
        }
        
        // Listen for install prompt
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('Install prompt available!');
            e.preventDefault();
            deferredPrompt = e;
            
            installBtn.style.display = 'inline-block';
            installStatus.innerHTML = '<p>✅ Ready to install!</p>';
        });
        
        // Listen for successful installation
        window.addEventListener('appinstalled', () => {
            console.log('App installed successfully');
            installStatus.innerHTML = '<p>🎉 App installed successfully!</p>';
            installBtn.style.display = 'none';
        });
        
        // Check if already installed
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (window.matchMedia('(display-mode: standalone)').matches) {
                    installStatus.innerHTML = '<p>✅ App is already installed!</p>';
                } else if (!deferredPrompt) {
                    installStatus.innerHTML = `
                        <p>⚠️ Install prompt not available yet</p>
                        <p>Try visiting the app multiple times and interacting with it</p>
                        <p>Or use manual installation methods below</p>
                    `;
                }
            }, 2000);
        });
        
        // No service worker registration in this version
        console.log('Simple PWA version - Service Worker skipped due to SSL certificate issues');
    </script>
</body>
</html>
