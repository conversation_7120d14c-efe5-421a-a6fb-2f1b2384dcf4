<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - PDF Print Tools</title>
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        .offline-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            text-align: center;
            padding: 20px;
        }
        
        .offline-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #7f8c8d;
        }
        
        .offline-title {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        .offline-message {
            font-size: 1.1rem;
            color: #7f8c8d;
            margin-bottom: 2rem;
            max-width: 500px;
            line-height: 1.6;
        }
        
        .retry-btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 1rem;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .retry-btn:hover {
            background-color: #2980b9;
        }
        
        .cached-links {
            margin-top: 2rem;
        }
        
        .cached-links h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        .cached-links a {
            display: inline-block;
            margin: 0.5rem;
            padding: 8px 16px;
            background-color: #ecf0f1;
            color: #2c3e50;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .cached-links a:hover {
            background-color: #bdc3c7;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📡</div>
        <h1 class="offline-title">You're Offline</h1>
        <p class="offline-message">
            It looks like you're not connected to the internet. 
            Some features may not be available, but you can still access cached content.
        </p>
        
        <button class="retry-btn" onclick="window.location.reload()">
            🔄 Try Again
        </button>
        
        <div class="cached-links">
            <h3>Available Pages:</h3>
            <a href="test-pdfjs-print.html">📄 PDF.js Print Test</a>
            <a href="test-pdf-print.html">🔄 Multi-Method Test</a>
        </div>
    </div>
    
    <script>
        // Check if we're back online
        window.addEventListener('online', () => {
            console.log('Back online!');
            // Optionally reload the page or show a notification
            if (confirm('You\'re back online! Reload the page?')) {
                window.location.reload();
            }
        });
        
        // Log offline status
        console.log('Offline page loaded. Online status:', navigator.onLine);
    </script>
</body>
</html>
