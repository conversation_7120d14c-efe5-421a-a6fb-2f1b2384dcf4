<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Test - PDF Print Tools</title>
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <h1>🧪 PWA Test</h1>
            <a href="test-pdfjs-print.html" class="nav-link">📄 PDF.js Test</a>
            <a href="test-pdf-print.html" class="nav-link">🔄 Multi-Method Test</a>
        </div>
    </div>
    
    <div class="test-container">
        <div class="test-section">
            <h2>PWA Feature Tests</h2>
            <p>This page tests various PWA features to ensure everything is working correctly.</p>
            
            <div id="test-results"></div>
            
            <button class="btn" onclick="runTests()">🧪 Run Tests</button>
        </div>
        
        <div class="test-section">
            <h2>Manual Tests</h2>
            <ul>
                <li><strong>Install Prompt:</strong> Check if browser shows install prompt</li>
                <li><strong>Offline Mode:</strong> Disconnect internet and reload page</li>
                <li><strong>Service Worker:</strong> Check browser dev tools > Application > Service Workers</li>
                <li><strong>Manifest:</strong> Check browser dev tools > Application > Manifest</li>
                <li><strong>Cache:</strong> Check browser dev tools > Application > Storage > Cache Storage</li>
            </ul>
        </div>
    </div>
    
    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<h3>Test Results:</h3>';
            
            // Test 1: Service Worker Support
            if ('serviceWorker' in navigator) {
                addResult('✓ Service Worker API supported', 'success');
                
                // Check if service worker is registered
                navigator.serviceWorker.getRegistration().then(registration => {
                    if (registration) {
                        addResult('✓ Service Worker registered: ' + registration.scope, 'success');
                    } else {
                        addResult('⚠ Service Worker not yet registered', 'info');
                    }
                });
            } else {
                addResult('✗ Service Worker API not supported', 'error');
            }
            
            // Test 2: Manifest Support
            if ('manifest' in document.createElement('link')) {
                addResult('✓ Web App Manifest supported', 'success');
            } else {
                addResult('⚠ Web App Manifest may not be fully supported', 'info');
            }
            
            // Test 3: Cache API
            if ('caches' in window) {
                addResult('✓ Cache API supported', 'success');
                
                // Check for cached resources
                caches.keys().then(cacheNames => {
                    if (cacheNames.length > 0) {
                        addResult('✓ Found ' + cacheNames.length + ' cache(s): ' + cacheNames.join(', '), 'success');
                    } else {
                        addResult('⚠ No caches found yet', 'info');
                    }
                });
            } else {
                addResult('✗ Cache API not supported', 'error');
            }
            
            // Test 4: Install Prompt
            addResult('ℹ Install prompt availability depends on browser criteria', 'info');

            // Check if app is already installed
            if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
                addResult('✓ App is running in standalone mode (already installed)', 'success');
            } else {
                addResult('ℹ App is running in browser mode (not installed)', 'info');
            }

            // Check for beforeinstallprompt event
            let installPromptReceived = false;
            window.addEventListener('beforeinstallprompt', () => {
                installPromptReceived = true;
                addResult('✓ Install prompt event received', 'success');
            });

            setTimeout(() => {
                if (!installPromptReceived) {
                    addResult('⚠ Install prompt not received yet. Possible reasons:', 'info');
                    addResult('  • App may already be installed', 'info');
                    addResult('  • Browser install criteria not met', 'info');
                    addResult('  • Using unsupported browser', 'info');
                    addResult('  • Not served over HTTPS (in production)', 'info');
                }
            }, 2000);
            
            // Test 5: Online/Offline Detection
            if ('onLine' in navigator) {
                addResult('✓ Online status: ' + (navigator.onLine ? 'Online' : 'Offline'), 'success');
            } else {
                addResult('⚠ Online status detection not supported', 'info');
            }
            
            // Test 6: Fetch API
            if ('fetch' in window) {
                addResult('✓ Fetch API supported', 'success');
            } else {
                addResult('✗ Fetch API not supported', 'error');
            }
            
            // Test 7: Check manifest file
            fetch('/manifest.json')
                .then(response => {
                    if (response.ok) {
                        addResult('✓ Manifest file accessible', 'success');
                        return response.json();
                    } else {
                        throw new Error('Manifest not found');
                    }
                })
                .then(manifest => {
                    addResult('✓ Manifest parsed successfully: ' + manifest.name, 'success');
                })
                .catch(error => {
                    addResult('✗ Manifest error: ' + error.message, 'error');
                });
            
            // Test 8: Check service worker file
            fetch('/sw.js')
                .then(response => {
                    if (response.ok) {
                        addResult('✓ Service Worker file accessible', 'success');
                    } else {
                        throw new Error('Service Worker file not found');
                    }
                })
                .catch(error => {
                    addResult('✗ Service Worker file error: ' + error.message, 'error');
                });
        }
        
        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runTests, 1000); // Wait a bit for service worker registration
        });
    </script>
</body>
</html>
